import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, Clock, ExternalLink, Wand2, ChevronDown, ChevronUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { renderMarkdown } from '@/utils/markdown';

interface CollapsibleSummaryCardProps {
  summary: any;
  platformNames: Record<string, string>;
  getPlatformFromSummaryType: (type: string) => string;
  getTitleFromMetadata: (metadata: any, url: string, index: number) => string;
  isFavorite: (id: string) => boolean;
  toggleFavorite: (id: string) => void;
  isSummaryFavorite: (id: string) => boolean;
  toggleSummaryFavorite: (id: string) => void;
  handleGenerateContent: (summary: any) => void;
  favoritesLoading: boolean;
  summaryFavoritesLoading: boolean;
}

const CollapsibleSummaryCard: React.FC<CollapsibleSummaryCardProps> = ({
  summary,
  platformNames,
  getPlatformFromSummaryType,
  getTitleFromMetadata,
  isFavorite,
  toggleFavorite,
  isSummaryFavorite,
  toggleSummaryFavorite,
  handleGenerateContent,
  favoritesLoading,
  summaryFavoritesLoading,
}) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  // 生成预览文本（去除markdown格式，限制长度）
  const getPreviewText = (content: string) => {
    const plainText = content
      .replace(/#{1,6}\s+/g, '') // 移除标题标记
      .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体标记
      .replace(/\*(.*?)\*/g, '$1') // 移除斜体标记
      .replace(/\[(.*?)\]\(.*?\)/g, '$1') // 移除链接，保留文本
      .replace(/`(.*?)`/g, '$1') // 移除代码标记
      .replace(/[-*]\s+/g, '') // 移除列表标记
      .replace(/\n+/g, ' ') // 将换行符替换为空格
      .trim();
    
    return plainText.length > 150 ? plainText.substring(0, 150) + '...' : plainText;
  };

  const previewText = getPreviewText(summary.content);

  return (
    <Card className="mobile-ultra-card mobile-card-expand hover:shadow-md transition-all duration-200 card-minimal">
      <CardHeader className="mobile-card-padding mobile-ultra-card mobile-text-padding md:pb-3">
        {/* 移动端：分两行显示 */}
        <div className="md:flex md:items-start md:justify-between">
          {/* 第一行：Badge标签 */}
          <div className="flex items-center mobile-badge-spacing gap-2 flex-1 mb-2 md:mb-0">
            <Badge variant="outline" className="mobile-ultra-badge">
              {summary.metadata?.topic_name || t('contentSummary.unknownTopic')}
            </Badge>
            <Badge variant="secondary" className="mobile-ultra-badge">
              {summary.metadata?.source_name || summary.posts?.datasources?.source_name || t('contentSummary.unknownSource')}
            </Badge>
            <Badge variant="outline" className="mobile-ultra-badge">
              {(() => {
                const platform = summary.metadata?.platform ||
                               summary.posts?.datasources?.platform ||
                               getPlatformFromSummaryType(summary.summary_type);
                return platformNames[platform as keyof typeof platformNames] || platform;
              })()}
            </Badge>
          </div>

          {/* 第二行：收藏按钮组（移动端单独一行，桌面端在右侧） */}
          <div className="flex items-center gap-2 md:ml-4">
            {/* 收藏数据源按钮 */}
            {(() => {
              // 从metadata中获取datasource_id
              const datasourceId = summary.metadata?.datasource_id;
              if (!datasourceId) return null;

              const isFavorited = isFavorite(datasourceId);

              return (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFavorite(datasourceId);
                  }}
                  disabled={favoritesLoading}
                  className="mobile-favorite-button md:h-8 md:px-2 md:text-xs hover:bg-accent"
                  title={isFavorited ? t('contentSummary.favorites.unfavoriteDataSource') : t('contentSummary.favorites.favoriteDataSource')}
                >
                  <Heart
                    className={`h-3 w-3 mr-1 ${
                      isFavorited
                        ? 'fill-red-500 text-red-500'
                        : 'text-muted-foreground hover:text-red-500'
                    }`}
                  />
                  <span className="mobile-text-xs md:text-xs truncate">
                    {isFavorited ? t('contentSummary.favorites.favorited') : t('contentSummary.favorites.favoriteDataSource')}
                  </span>
                </Button>
              );
            })()}

            {/* 收藏摘要按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                toggleSummaryFavorite(summary.id);
              }}
              disabled={summaryFavoritesLoading}
              className="mobile-favorite-button md:h-8 md:px-2 md:text-xs hover:bg-accent"
              title={isSummaryFavorite(summary.id) ? t('contentSummary.favorites.unfavoriteSummary') : t('contentSummary.favorites.favoriteSummary')}
            >
              <Heart
                className={`h-3 w-3 mr-1 ${
                  isSummaryFavorite(summary.id)
                    ? 'fill-blue-500 text-blue-500'
                    : 'text-muted-foreground hover:text-blue-500'
                }`}
              />
              <span className="mobile-text-xs md:text-xs truncate">
                {isSummaryFavorite(summary.id) ? t('contentSummary.favorites.favorited') : t('contentSummary.favorites.favoriteSummary')}
              </span>
            </Button>

            {/* 时间信息 - 只在桌面端显示 */}
            <div className="hidden md:flex items-center gap-1 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              {new Date(summary.created_at).toLocaleString()}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="mobile-card-padding mobile-ultra-card mobile-content-ultra mobile-text-padding">
        <div className="space-y-1 md:space-y-3">
          {/* 摘要内容预览/完整内容 */}
          <div className="max-w-none">
            {isExpanded ? (
              <div
                className="mobile-text-sm mobile-markdown-content md:text-sm leading-relaxed text-foreground whitespace-pre-line markdown-content"
                dangerouslySetInnerHTML={{
                  __html: renderMarkdown(summary.content)
                }}
              />
            ) : (
              <div className="mobile-text-sm mobile-markdown-content md:text-sm leading-relaxed text-foreground">
                {previewText}
              </div>
            )}
          </div>

          {/* 展开/折叠按钮 */}
          <div className="flex justify-center pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-primary hover:bg-accent h-8 px-3"
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-1" />
                  收起全文
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" />
                  展开全文
                </>
              )}
            </Button>
          </div>

          {/* 相关链接 - 只在展开时显示 */}
          {isExpanded && summary.source_urls && summary.source_urls.length > 0 && (
            <div className="space-y-2">
              <div className="mobile-text-sm md:text-sm font-medium text-muted-foreground">相关链接:</div>
              <div className="space-y-1">
                {summary.source_urls.slice(0, 5).map((url: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <ExternalLink className="h-3 w-3 text-muted-foreground" />
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="mobile-text-sm md:text-sm text-blue-600 hover:text-blue-800 underline truncate"
                      title={getTitleFromMetadata(summary.metadata, url, index)}
                    >
                      {getTitleFromMetadata(summary.metadata, url, index)}
                    </a>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 生成内容按钮 - 只在展开时显示 */}
          {isExpanded && (
            <div className="pt-3 border-t">
              <Button
                onClick={() => handleGenerateContent(summary)}
                variant="outline"
                size="sm"
                className="w-full"
              >
                <Wand2 className="h-4 w-4 mr-2" />
                {t('contentSummary.summary.generateContent')}
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CollapsibleSummaryCard;
